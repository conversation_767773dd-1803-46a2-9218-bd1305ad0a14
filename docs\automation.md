# 定期自动爬取微博（可选）

我们爬取了微博以后，很多微博账号又可能发了一些新微博，定期自动爬取微博就是每隔一段时间自动运行程序，自动爬取这段时间产生的新微博（忽略以前爬过的旧微博）。本部分为可选部分，如果不需要可以忽略。

思路是**利用第三方软件，如crontab，让程序每隔一段时间运行一次**。因为是要跳过以前爬过的旧微博，只爬新微博。所以需要**设置一个动态的since_date**。很多时候我们使用的since_date是固定的，比如since_date="2018-01-01"，程序就会按照这个设置从最新的微博一直爬到发布时间为2018-01-01的微博（包括这个时间）。因为我们想追加新微博，跳过旧微博。第二次爬取时since_date值就应该是当前时间到上次爬取的时间。
如果我们使用最原始的方式实现追加爬取，应该是这样：

```text
假如程序第一次执行时间是2019-06-06，since_date假如为2018-01-01，那这一次就是爬取从2018-01-01到2019-06-06这段时间用户所发的微博；
第二次爬取，我们想要接着上次的爬，那since_date的值应该是上次程序执行的日期，即2019-06-06
```

上面的方法太麻烦，因为每次都要手动设置since_date。因此我们需要动态设置since_date，即程序根据实际情况，自动生成since_date。

有两种方法实现动态更新since_date，**推荐使用方法二**。

## 方法一：将since_date设置成整数

将config.json文件中的since_date设置成整数，如：

```json
"since_date": 10,
```

这个配置告诉程序爬取最近10天的微博，更准确说是爬取发布时间从**10天前到本程序开始执行时**之间的微博。这样since_date就是一个动态的变量，每次程序执行时，它的值就是当前日期减10。配合crontab每9天或10天执行一次，就实现了定期追加爬取。

## 方法二：将上次执行程序的时间写入文件（推荐）

这个方法很简单，就是使用[程序设置](https://github.com/dataabc/weiboSpider/blob/master/docs/settings.md)中**设置user_id_list**的第二种方法设置user_id_list，这样设置就全部结束了。

说下这个方法的好处和原理，假如你的txt文件内容为：

```text
1669879400
1223178222 胡歌
1729370543 郭碧婷 2019-01-01 19:28
```

第一次执行时，因为第一行和第二行都没有写时间，程序会按照config.json文件中since_date的值爬取，第三行有时间“2019-01-01 19:28”，程序就会把这个时间当作since_date。每个用户爬取结束程序都会自动更新txt文件，每一行第一部分是user_id，第二部分是用户昵称，第三部分是程序**准备**爬取该用户第一条微博（最新微博）时的时间。爬完三个用户后，txt文件的内容自动更新为：

```text
1669879400 Dear-迪丽热巴 2020-01-13 19:18
1223178222 胡歌 2020-01-13 19:28
1729370543 郭碧婷 2020-01-13 19:33
```

下次再爬取微博的时候，程序会把每行的时间数据作为since_date。这样的好处一是不用修改since_date，程序自动更新；二是每一个用户都可以单独拥有只属于自己的since_date，每个用户的since_date相互独立，互不干扰。since_date既可以是“yyyy-mm-dd”格式，也可以是“yyyy-mm-dd hh:mm”格式。比如，现在又添加了一个新用户，例如杨紫，你想获取她从2018-01-23到现在的全部微博，只需要这样修改txt文件：

```text
1669879400 Dear-迪丽热巴 2020-01-13 19:18
1223178222 胡歌 2020-01-13 19:28
1729370543 郭碧婷 2020-01-13 19:33
1227368500 杨紫 2018-01-23
```

注意每一行的用户配置参数以空格分隔，如果第一个参数全部由数字组成，程序就认为此行为一个用户的配置，否则程序会认为该行只是注释，跳过该行；第二个参数可以为任意格式，建议写用户昵称；第三个如果是日期格式（yyyy-mm-dd），程序就将该日期设置为用户自己的since_date，否则使用config.json中的since_date爬取该用户的微博，第二个参数和第三个参数也可以不填。

推荐第二种方法，本方法是[Evifly](https://github.com/Evifly)想出的，非常热心非常有想法的网友，在此感谢。
