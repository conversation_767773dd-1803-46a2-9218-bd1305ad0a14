---
name: Bug报修
about: 向程序开发者申报bug
title: ''
labels: bug
assignees: ''

---

感谢您申报bug，为了表示感谢，如果bug确实存在，您将出现在本项目的贡献者列表里；如果您不但发现了bug，还提供了很好的解决方案，我们会邀请您以pull request的方式成为本项目的代码贡献者（Contributor）；如果您多次提供很好的pull request，我们将邀请您成为本项目的协助者（Collaborator）。当然，是否提供解决方按都是自愿的。不管是否是真正的bug、是否提供解决方案，我们都感谢您对本项目的帮助。

- 问：请您指明哪个版本出了bug（github版/PyPi版/全部）？

答：

- 问：您使用的是否是最新的程序（是/否）？

答：

- 问：爬取任意用户都会复现此bug吗（是/否）？

答：

- 问：若只有爬特定微博时才出bug，能否提供出错微博的weibo_id或url（非必填）？

答：

- 问：若您已提供出错微博的weibo_id或url，可忽略此内容，否则能否提供出错账号的**user_id**及您配置的**since_date**，方便我们定位出错微博（非必填）？

答：

- 问：如果方便，请您描述bug详情，如果代码报错，最好附上错误提示。

答：
