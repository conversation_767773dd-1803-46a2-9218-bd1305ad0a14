[loggers]
keys=root,spider

[handlers]
keys=<PERSON><PERSON><PERSON><PERSON>,file<PERSON><PERSON><PERSON>,error<PERSON><PERSON><PERSON>

[formatters]
keys=console<PERSON><PERSON>atter,fileFormatter,errorFormatter

[logger_root]
level=DEBUG
handlers=console<PERSON><PERSON><PERSON>,fileHandler,errorHandler

[logger_spider]
level=DEBUG
handlers=console<PERSON><PERSON><PERSON>,fileHandler,errorHandler
qualname=spider
propagate=0

[handler_consoleHandler]
class=StreamHandler
level=DEBUG
formatter=consoleFormatter
args=(sys.stdout,)

[handler_fileHandler]
class=handlers.TimedRotatingFileHandler
level=INFO
formatter=fileFormatter
args=('all.log', 'D', 1, 5, 'utf-8', False, False)

[handler_errorHandler]
class=FileHandler
level=WARNING
formatter=errorFormatter
args=('error.log', 'a','utf-8')

[formatter_consoleFormatter]
format=%(message)s

[formatter_fileFormatter]
format=%(asctime)s - %(filename)s - %(levelname)s - %(message)s

[formatter_errorFormatter]
format=%(asctime)s - %(levelname)s - %(filename)s[:%(lineno)d] - %(message)s